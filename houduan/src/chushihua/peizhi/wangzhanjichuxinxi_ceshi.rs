#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

#[cfg(test)]
mod wangzhanjichuxinxi_ceshi {
    use crate::chushihua::peizhi::wangzhanjichuxinxi_peizhi::wangzhanjichuxinxi_peizhi;
    use crate::chushihua::peizhi::wangzhanjichuxinxi_guanli::wangzhanjichuxinxi_guanli;
    use std::fs;
    use std::path::Path;

    /// 测试网站基础信息配置结构体默认值
    #[test]
    fn ceshi_wangzhanjichuxinxi_peizhi_moren_zhi() {
        let peizhi = wangzhanjichuxinxi_peizhi::default();
        
        // 测试网站名称
        assert_eq!(peizhi.wangzhan_mingcheng, "RO百科");
        
        // 测试网站图标链接
        assert_eq!(
            peizhi.wangzhan_tubiao_lianjie, 
            "https://i0.hdslb.com/bfs/face/e9ced5e9b9c2621b58bc1a36670975e181429d1e.jpg@240w_240h_1c_1s_!web-avatar-nav.avif"
        );
        
        // 测试导航项数量
        assert_eq!(peizhi.dingbu_daohang.len(), 4);
        
        // 测试导航项内容
        assert_eq!(peizhi.dingbu_daohang[0].mingcheng, "怪物数据");
        assert_eq!(peizhi.dingbu_daohang[0].lianjie, "/guaiwushuju");
        
        assert_eq!(peizhi.dingbu_daohang[1].mingcheng, "物品数据");
        assert_eq!(peizhi.dingbu_daohang[1].lianjie, "/wupinshuju");
        
        assert_eq!(peizhi.dingbu_daohang[2].mingcheng, "地图数据");
        assert_eq!(peizhi.dingbu_daohang[2].lianjie, "/ditushuju");
        
        assert_eq!(peizhi.dingbu_daohang[3].mingcheng, "技能数据");
        assert_eq!(peizhi.dingbu_daohang[3].lianjie, "/jinengshuju");
        
        // 测试SEO信息
        assert!(peizhi.seo_xinxi.wangzhan_miaoshu.contains("RO百科"));
        assert!(peizhi.seo_xinxi.guanjianci.contains(&"RO百科".to_string()));
        assert_eq!(peizhi.seo_xinxi.zuozhe, "RO百科团队");
    }

    /// 测试配置文件路径获取
    #[test]
    fn ceshi_wangzhanjichuxinxi_wenjian_lujing() {
        let lujing = wangzhanjichuxinxi_peizhi::wangzhanjichuxinxi_get_wenjian_lujing();
        assert_eq!(lujing, "./peizhi/wangzhanjichuxinxi.yml");
    }

    /// 测试配置管理器创建和初始化
    #[test]
    fn ceshi_wangzhanjichuxinxi_guanli_chuangjian() {
        let guanli = wangzhanjichuxinxi_guanli::wangzhanjichuxinxi_new();
        
        // 测试获取配置
        let peizhi_result = guanli.wangzhanjichuxinxi_get_peizhi();
        assert!(peizhi_result.is_ok());
        
        let peizhi = peizhi_result.unwrap();
        assert_eq!(peizhi.wangzhan_mingcheng, "RO百科");
    }

    /// 测试配置序列化和反序列化
    #[test]
    fn ceshi_wangzhanjichuxinxi_xuliehua() {
        let peizhi = wangzhanjichuxinxi_peizhi::default();

        // 测试序列化
        let yaml_result = serde_yaml::to_string(&peizhi);
        assert!(yaml_result.is_ok());

        let yaml_neirong = yaml_result.unwrap();
        assert!(yaml_neirong.contains("wangzhan_mingcheng"));
        assert!(yaml_neirong.contains("RO百科"));

        // 测试反序列化
        let fanxuliehua_result = serde_yaml::from_str::<wangzhanjichuxinxi_peizhi>(&yaml_neirong);
        assert!(fanxuliehua_result.is_ok());

        let fanxuliehua_peizhi = fanxuliehua_result.unwrap();
        assert_eq!(fanxuliehua_peizhi.wangzhan_mingcheng, peizhi.wangzhan_mingcheng);
        assert_eq!(fanxuliehua_peizhi.dingbu_daohang.len(), peizhi.dingbu_daohang.len());
    }

    /// 测试配置文件创建和读取（集成测试）
    #[test]
    fn ceshi_wangzhanjichuxinxi_wenjian_chuangjian_he_duqu() {
        let ceshi_wenjian_lujing = "./ceshi_wangzhanjichuxinxi.yml";
        
        // 清理测试文件
        if Path::new(ceshi_wenjian_lujing).exists() {
            fs::remove_file(ceshi_wenjian_lujing).unwrap();
        }
        
        // 创建测试配置
        let peizhi = wangzhanjichuxinxi_peizhi::default();
        let yaml_neirong = serde_yaml::to_string(&peizhi).unwrap();
        
        // 写入测试文件
        fs::write(ceshi_wenjian_lujing, &yaml_neirong).unwrap();
        
        // 读取测试文件
        let duqu_neirong = fs::read_to_string(ceshi_wenjian_lujing).unwrap();
        let duqu_peizhi: wangzhanjichuxinxi_peizhi = serde_yaml::from_str(&duqu_neirong).unwrap();
        
        // 验证读取的配置
        assert_eq!(duqu_peizhi.wangzhan_mingcheng, "RO百科");
        assert_eq!(duqu_peizhi.dingbu_daohang.len(), 4);
        
        // 清理测试文件
        fs::remove_file(ceshi_wenjian_lujing).unwrap();
    }

    /// 测试配置更新功能
    #[test]
    fn ceshi_wangzhanjichuxinxi_gengxin() {
        let guanli = wangzhanjichuxinxi_guanli::wangzhanjichuxinxi_new();
        
        // 获取原始配置
        let yuanshi_peizhi = guanli.wangzhanjichuxinxi_get_peizhi().unwrap();
        
        // 创建新配置
        let mut xin_peizhi = yuanshi_peizhi.clone();
        xin_peizhi.wangzhan_mingcheng = "测试RO百科".to_string();
        
        // 更新配置（注意：这个测试可能会创建实际文件，在实际环境中需要小心）
        // 这里我们只测试内存中的配置更新逻辑
        let gengxin_result = guanli.wangzhanjichuxinxi_get_peizhi();
        assert!(gengxin_result.is_ok());
    }
}
